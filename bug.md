PS D:\project\ai-dance\legend_dance> flutter run -d FMR0224924017063
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on ALN AL00 in debug mode...
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
lib/pages/creation/services/my_works_service.dart:279:29: Error: A value of type 'DanceCategoryType' can't be assigned to a variable of type 'DanceCategory?'.
 - 'DanceCategoryType' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
 - 'DanceCategory' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
              newCategory = categoryType; // 直接使用 DanceCategoryType
                            ^
lib/pages/creation/services/my_works_service.dart:282:31: Error: A value of type 'DanceCategory/*1*/' can't be assigned to a variable of type 'DanceCategory/*2*/?'.
 - 'DanceCategory/*1*/' is from 'package:keepdance/models/local_dance_video.dart' ('lib/models/local_dance_video.dart').
 - 'DanceCategory/*2*/' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
          newCategory = video.category;
                              ^
lib/pages/creation/services/my_works_service.dart:288:85: Error: A value of type '_Enum' can't be assigned to a variable of type 'DanceLevel?'.
 - '_Enum' is from 'dart:core'.
 - 'DanceLevel' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
          newLevel = DanceLevel.values.firstWhereOrNull((e) => e.name == levelName) ?? video.level;
                                                                                    ^
lib/pages/creation/services/my_works_service.dart:290:28: Error: A value of type 'DanceLevel/*1*/' can't be assigned to a variable of type 'DanceLevel/*2*/?'.
 - 'DanceLevel/*1*/' is from 'package:keepdance/models/local_dance_video.dart' ('lib/models/local_dance_video.dart').
 - 'DanceLevel/*2*/' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
          newLevel = video.level;
                           ^
lib/pages/creation/services/my_works_service.dart:296:97: Error: A value of type '_Enum' can't be assigned to a variable of type 'DanceIntensity?'.
 - '_Enum' is from 'dart:core'.
 - 'DanceIntensity' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
          newIntensity = DanceIntensity.values.firstWhereOrNull((e) => e.name == intensityName) ?? video.intensity;
                                                                                          ^
lib/pages/creation/services/my_works_service.dart:298:32: Error: A value of type 'DanceIntensity/*1*/' can't be assigned to a variable of type 'DanceIntensity/*2*/?'.
 - 'DanceIntensity/*1*/' is from 'package:keepdance/models/local_dance_video.dart' ('lib/models/local_dance_video.dart').
 - 'DanceIntensity/*2*/' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
          newIntensity = video.intensity;
                               ^
lib/pages/creation/services/my_works_service.dart:309:19: Error: The argument type 'DanceCategory/*1*/?' can't be assigned to the parameter type 'DanceCategory/*2*/?'.
 - 'DanceCategory/*1*/' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
 - 'DanceCategory/*2*/' is from 'package:keepdance/models/local_dance_video.dart' ('lib/models/local_dance_video.dart').
        category: newCategory,
                  ^
lib/pages/creation/services/my_works_service.dart:310:16: Error: The argument type 'DanceLevel/*1*/?' can't be assigned to the parameter type 'DanceLevel/*2*/?'.
 - 'DanceLevel/*1*/' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
 - 'DanceLevel/*2*/' is from 'package:keepdance/models/local_dance_video.dart' ('lib/models/local_dance_video.dart').
        level: newLevel,
               ^
lib/pages/creation/services/my_works_service.dart:311:20: Error: The argument type 'DanceIntensity/*1*/?' can't be assigned to the parameter type 'DanceIntensity/*2*/?'.
 - 'DanceIntensity/*1*/' is from 'package:keepdance/pages/creation/services/my_works_service.dart' ('lib/pages/creation/services/my_works_service.dart').
 - 'DanceIntensity/*2*/' is from 'package:keepdance/models/local_dance_video.dart' ('lib/models/local_dance_video.dart').
        intensity: newIntensity,
                   ^
lib/pages/creation/views/widgets/creation/filter_sort_controls.dart:35:57: Error: The getter 'value' isn't defined for the class 'String'.
Try correcting the name to the name of an existing getter, or defining a getter or field named 'value'.
              isSelected: controller.selectedCategoryId.value == null,
                                                        ^^^^^
lib/pages/creation/views/widgets/creation/filter_sort_controls.dart:50:57: Error: The getter 'value' isn't defined for the class 'String'.
Try correcting the name to the name of an existing getter, or defining a getter or field named 'value'.
              isSelected: controller.selectedCategoryId.value?.id == category.id,
                                                        ^^^^^
lib/pages/creation/views/widgets/creation/multi_select_bottom_bar.dart:55:17: Error: No named parameter with the name 'text'.
                text: "删除",
                ^^^^
lib/pages/creation/views/widgets/creation/action_button.dart:16:9: Context: Found this candidate, but the arguments don't match.
  const ActionButton({
        ^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/multi_select_bottom_bar.dart:33:29: Error: The argument type 'double' can't be assigned to the parameter type 'Radius'.
 - 'Radius' is from 'dart:ui'.
          topLeft: AppTheme.smallRadius,
                            ^
lib/pages/creation/views/widgets/creation/multi_select_bottom_bar.dart:34:30: Error: The argument type 'double' can't be assigned to the parameter type 'Radius'.
 - 'Radius' is from 'dart:ui'.
          topRight: AppTheme.smallRadius,
                             ^
lib/pages/creation/views/widgets/creation/multi_select_header.dart:44:9: Error: No named parameter with the name 'clipBehavior'.
        clipBehavior: Clip.none,
        ^^^^^^^^^^^^
/D:/Program%20Files/Flutterdev/flutter/packages/flutter/lib/src/widgets/basic.dart:5166:9: Context: Found this candidate, but the arguments don't match.
  const Row({
        ^^^
lib/pages/creation/views/widgets/creation/work_item_wrapper.dart:55:9: Error: No named parameter with the name 'workItemData'.
        workItemData: workItemData,
        ^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:48:9: Context: Found this candidate, but the arguments don't match.
  const WorkItemCard({
        ^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_wrapper.dart:35:67: Error: A value of type 'WorkItemData?' can't be assigned to a variable of type 'WorkItemData' because 'WorkItemData?' is nullable and 'WorkItemData' isn't.
 - 'WorkItemData' is from 'package:keepdance/models/work_item_data.dart' ('lib/models/work_item_data.dart').
      final WorkItemData currentWork = controller.currentWorkItem.value;
                                                                  ^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:79:43: Error: The getter 'isScanning' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isScanning'.
    double progress = _creationController.isScanning.value ? 0.2 : 0.0;
                                          ^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:81:29: Error: The getter 'totalCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'totalCount'.
    if (_creationController.totalCount.value > 0) {
                            ^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:82:50: Error: The getter 'processedCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'processedCount'.
      final processedRatio = _creationController.processedCount.value / _creationController.totalCount.value;
                                                 ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:82:93: Error: The getter 'totalCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'totalCount'.
      final processedRatio = _creationController.processedCount.value / _creationController.totalCount.value;
                                                                                          ^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:88:29: Error: The getter 'isCategorizing' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCategorizing'.
    if (_creationController.isCategorizing.value) {
                            ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:89:31: Error: The getter 'processedCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'processedCount'.
      if (_creationController.processedCount.value > 0) {
                              ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:94:47: Error: The getter 'isDone' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isDone'.
      final bool isDone = _creationController.isDone.value;
                                              ^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:105:29: Error: The getter 'totalCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'totalCount'.
    if (_creationController.totalCount.value > 0 &&
                            ^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:106:29: Error: The getter 'processedCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'processedCount'.
        _creationController.processedCount.value > 0) {
                            ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:107:37: Error: The getter 'processedCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'processedCount'.
      return '${_creationController.processedCount.value}/${_creationController.totalCount.value} 个作品';
                                    ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_loading_state.dart:107:81: Error: The getter 'totalCount' isn't defined for the class 'CreationController'.
 - 'CreationController' is from 'package:keepdance/pages/creation/controllers/creation_controller.dart' ('lib/pages/creation/controllers/creation_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'totalCount'.
      return '${_creationController.processedCount.value}/${_creationController.totalCount.value} 个作品';
                                                                                ^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_search_bar.dart:200:39: Error: The getter 'BoxHeightStyle' isn't defined for the class 'WorksSearchBar'.
 - 'WorksSearchBar' is from 'package:keepdance/pages/creation/views/widgets/creation/works_search_bar.dart' ('lib/pages/creation/views/widgets/creation/works_search_bar.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'BoxHeightStyle'.
                selectionHeightStyle: BoxHeightStyle.tight,
                                      ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_search_bar.dart:201:38: Error: The getter 'BoxWidthStyle' isn't defined for the class 'WorksSearchBar'.
 - 'WorksSearchBar' is from 'package:keepdance/pages/creation/views/widgets/creation/works_search_bar.dart' ('lib/pages/creation/views/widgets/creation/works_search_bar.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'BoxWidthStyle'.
                selectionWidthStyle: BoxWidthStyle.tight,
                                     ^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/works_search_bar.dart:216:50: Error: The argument type 'bool' can't be assigned to the parameter type 'Widget Function(BuildContext, EditableTextState)?'.
 - 'Widget' is from 'package:flutter/src/widgets/framework.dart' ('/D:/Program%20Files/Flutterdev/flutter/packages/flutter/lib/src/widgets/framework.dart').
 - 'BuildContext' is from 'package:flutter/src/widgets/framework.dart' ('/D:/Program%20Files/Flutterdev/flutter/packages/flutter/lib/src/widgets/framework.dart').
 - 'EditableTextState' is from 'package:flutter/src/widgets/editable_text.dart' ('/D:/Program%20Files/Flutterdev/flutter/packages/flutter/lib/src/widgets/editable_text.dart').
                contextMenuBuilder: EditableText.debugDeterministicCursor, // _defaultContextMenuBuilder
                                                 ^
lib/pages/creation/views/widgets/creation/delete_confirm_dialog.dart:49:71: Error: Too few positional arguments: 2 required, 0 given.
    final bool? confirmed = await CommonDialog.showDeleteConfirmDialog(
                                                                      ^
lib/common_widgets/common_dialog.dart:214:24: Context: Found this candidate, but the arguments don't match.
  static Future<bool?> showDeleteConfirmDialog(
                       ^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:190:45: Error: Member not found: 'instance'.
      final calculator = CaloriesCalculator.instance;
                                            ^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:323:45: Error: Method 'toIso8601String' cannot be called on 'DateTime?' because it is potentially null.
 - 'DateTime' is from 'dart:core'.
Try calling using ?. instead.
        'publishTime': workItem.publishTime.toIso8601String(),
                                            ^^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:440:72: Error: Method 'withOpacity' cannot be called on 'Color?' because it is potentially null.
 - 'Color' is from 'dart:ui'.
Try calling using ?. instead.
              color: (isSelected ? AppTheme.color(100) : Colors.white).withOpacity(0.8),
                                                                       ^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:605:49: Error: Too few positional arguments: 1 required, 0 given.
              child: RatingBadge.forMaterialCard(
                                                ^
lib/common_widgets/rating_badge.dart:30:11: Context: Found this candidate, but the arguments don't match.
  factory RatingBadge.forMaterialCard(dynamic score) {
          ^^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:690:57: Error: The method '_buildMirrorIndicator' isn't defined for the class '_WorkItemCardState'.
 - '_WorkItemCardState' is from 'package:keepdance/pages/creation/views/widgets/creation/work_item_card.dart' ('lib/pages/creation/views/widgets/creation/work_item_card.dart').
Try correcting the name to the name of an existing method, or defining a method named '_buildMirrorIndicator'.
                      children: [ SizedBox(width: 4.w), _buildMirrorIndicator()],
                                                        ^^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/work_item_card.dart:727:11: Error: No named parameter with the name 'imageUrl'.
          imageUrl: coverPath,
          ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:53:69: Error: Method not found: 'ApiClient'.
  final InteractionService _interactionService = InteractionService(ApiClient(), Logger());
                                                                    ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:198:16: Error: The method 'trackButtonClick' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'trackButtonClick'.
              .trackButtonClick('like', action, trackCallback: trackCallback);
               ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:242:16: Error: The method 'trackButtonClick' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'trackButtonClick'.
              .trackButtonClick('like', action, trackCallback: trackCallback);
               ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:293:16: Error: The method 'trackButtonClick' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'trackButtonClick'.
              .trackButtonClick('collect', action, trackCallback: trackCallback);
               ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:338:16: Error: The method 'trackButtonClick' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'trackButtonClick'.
              .trackButtonClick('collect', action, trackCallback: trackCallback);
               ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:373:16: Error: The method 'trackButtonClick' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'trackButtonClick'.
              .trackButtonClick('follow', action, trackCallback: trackCallback);
               ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:391:54: Error: The getter 'currentResolution' isn't defined for the class 'PlayerSettingsState'.
 - 'PlayerSettingsState' is from 'package:keepdance/pages/video_detail/states/player_settings_state.dart' ('lib/pages/video_detail/states/player_settings_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentResolution'.
  Rx<String> get currentResolution => _settingsState.currentResolution;
                                                     ^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:393:46: Error: The getter 'isPrivacyMode' isn't defined for the class 'PlayerSettingsState'.
 - 'PlayerSettingsState' is from 'package:keepdance/pages/video_detail/states/player_settings_state.dart' ('lib/pages/video_detail/states/player_settings_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isPrivacyMode'.
  RxBool get isPrivacyMode => _settingsState.isPrivacyMode;
                                             ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:394:43: Error: The getter 'isMirrored' isn't defined for the class 'PlayerSettingsState'.
 - 'PlayerSettingsState' is from 'package:keepdance/pages/video_detail/states/player_settings_state.dart' ('lib/pages/video_detail/states/player_settings_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isMirrored'.
  RxBool get isMirrored => _settingsState.isMirrored;
                                          ^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:395:50: Error: The getter 'isSplitScreenMode' isn't defined for the class 'PlayerSettingsState'.
 - 'PlayerSettingsState' is from 'package:keepdance/pages/video_detail/states/player_settings_state.dart' ('lib/pages/video_detail/states/player_settings_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isSplitScreenMode'.
  RxBool get isSplitScreenMode => _settingsState.isSplitScreenMode;
                                                 ^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:404:49: Error: Too few positional arguments: 5 required, 0 given.
    await VideoPreferences.saveVideoPlaySettings(
                                                ^
lib/pages/video_detail/utils/video_preferences.dart:13:23: Context: Found this candidate, but the arguments don't match.
  static Future<void> saveVideoPlaySettings(
                      ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:420:56: Error: Member not found: 'portrait'.
          settings['orientation'] ?? ScreenOrientation.portrait;
                                                       ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:442:25: Error: The getter 'type' isn't defined for the class 'DanceVideoDetail'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'type'.
        if (videoDetail.type == DetailType.community) {
                        ^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:443:38: Error: The getter 'detail' isn't defined for the class 'DanceVideoDetail'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'detail'.
          mirrorState = (videoDetail.detail?.mirror == 2);
                                     ^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:444:32: Error: The getter 'type' isn't defined for the class 'DanceVideoDetail'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'type'.
        } else if (videoDetail.type == DetailType.dance) {
                               ^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:445:38: Error: The getter 'dance' isn't defined for the class 'DanceVideoDetail'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'dance'.
          mirrorState = (videoDetail.dance?.mirror == 2);
                                     ^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:469:53: Error: The getter 'isPlayerInitialized' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isPlayerInitialized'.
  RxBool get isPlayerInitialized => _videoInfoState.isPlayerInitialized;
                                                    ^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:557:34: Error: Member not found: 'instance'.
      await PoseCameraController.instance.dispose();
                                 ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:571:45: Error: A value of type 'RxList<dynamic>' can't be assigned to a variable of type 'List<String>'.
 - 'RxList' is from 'package:get/get_rx/src/rx_types/rx_types.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib/get_rx/src/rx_types/rx_types.dart').
 - 'List' is from 'dart:core'.
    GlobalData.instance.localVideoPath = [].obs;
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:669:13: Error: No named parameter with the name 'useAsmsVideoTracks'.
            useAsmsVideoTracks: true,
            ^^^^^^^^^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/configuration/better_player_data_source.dart:79:3: Context: Found this candidate, but the arguments don't match.
  BetterPlayerDataSource(
  ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:579:5: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
    _isInitializing.value = true;
    ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:582:41: Error: Too few positional arguments: 1 required, 0 given.
    final videoUrlList = getVideoUrlList();
                                        ^
lib/pages/video_detail/controllers/video_detail_controller.dart:660:11: Error: The setter '_playerController' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing setter, or defining a setter or field named '_playerController'.
          _playerController = BetterPlayerController(config);
          ^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:675:15: Error: The getter '_playerController' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_playerController'.
          if (_playerController != null) {
              ^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:676:19: Error: The getter '_playerController' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_playerController'.
            await _playerController!.setupDataSource(dataSource);
                  ^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:677:19: Error: The getter '_playerController' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_playerController'.
            await _playerController!.play();
                  ^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:681:11: Error: The getter '_isPlaying' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isPlaying'.
          _isPlaying.value = true;
          ^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:683:11: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
          _isInitializing.value = false;
          ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:691:11: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
          _isInitializing.value = false;
          ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:700:11: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
          _isInitializing.value = false;
          ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:707:7: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
      _isInitializing.value = false;
      ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:712:7: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
      _isInitializing.value = false;
      ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:715:7: Error: The getter '_isInitializing' isn't defined for the class 'VideoPlayerMixin'.
 - 'VideoPlayerMixin' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isInitializing'.
      _isInitializing.value = false;
      ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:726:53: Error: The getter 'currentPlaybackSpeed' isn't defined for the class 'PlaybackSpeedState'.
 - 'PlaybackSpeedState' is from 'package:keepdance/pages/video_detail/states/playback_speed_state.dart' ('lib/pages/video_detail/states/playback_speed_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentPlaybackSpeed'.
    Rx<int> get currentPlaybackSpeed => _speedState.currentPlaybackSpeed;
                                                    ^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:727:41: Error: The getter 'isVipUser' isn't defined for the class 'PlaybackSpeedState'.
 - 'PlaybackSpeedState' is from 'package:keepdance/pages/video_detail/states/playback_speed_state.dart' ('lib/pages/video_detail/states/playback_speed_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isVipUser'.
    RxBool get isVipUser => _speedState.isVipUser;
                                        ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:740:32: Error: This expression has type 'void' and can't be used.
                 _speedService.onInit().catchError((e, s) {
                               ^
lib/pages/video_detail/controllers/video_detail_controller.dart:740:41: Error: The method 'catchError' isn't defined for the class 'void'.
Try correcting the name to the name of an existing method, or defining a method named 'catchError'.
                 _speedService.onInit().catchError((e, s) {
                                        ^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:767:49: Error: The method '_showVipUpgradeDialog' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named '_showVipUpgradeDialog'.
                (this as VideoDetailController)._showVipUpgradeDialog(speed);
                                                ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:774:55: Error: The method '_applySpeedToPlayerDirectly' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named '_applySpeedToPlayerDirectly'.
                await (this as VideoDetailController)._applySpeedToPlayerDirectly(speed);
                                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:775:49: Error: The method 'onSpeedChanged' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'onSpeedChanged'.
                (this as VideoDetailController).onSpeedChanged(speed);
                                                ^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:800:54: Error: Method not found: 'ApiClient'.
  final ReportService _reportService = ReportService(ApiClient());
                                                     ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:807:43: Error: The getter 'heroTag' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'heroTag'.
  RxString get heroTag => _videoInfoState.heroTag;
                                          ^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:810:43: Error: The getter 'isLoading' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoading'.
  RxBool get isLoading => _videoInfoState.isLoading;
                                          ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:811:48: Error: The getter 'currentTabIndex' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentTabIndex'.
  RxInt get currentTabIndex => _videoInfoState.currentTabIndex;
                                               ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:812:52: Error: The getter 'previousRoute' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'previousRoute'.
  Rx<String?> get previousRoute => _videoInfoState.previousRoute;
                                                   ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:813:45: Error: The getter 'isCommunity' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCommunity'.
  RxBool get isCommunity => _videoInfoState.isCommunity;
                                            ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:815:63: Error: The getter 'scoreHistory' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'scoreHistory'.
  RxList<LocalVideoScore> get scoreHistory => _videoInfoState.scoreHistory;
                                                              ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:816:50: Error: The getter 'isLoadingHistory' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoadingHistory'.
  RxBool get isLoadingHistory => _videoInfoState.isLoadingHistory;
                                                 ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:817:64: Error: The getter 'scoreStatistics' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'scoreStatistics'.
  RxMap<String, String> get scoreStatistics => _videoInfoState.scoreStatistics;
                                                               ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:818:53: Error: The getter 'localVideoStringId' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'localVideoStringId'.
  String? get localVideoStringId => _videoInfoState.localVideoStringId;
                                                    ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:819:40: Error: A value of type 'RxString' can't be returned from a function with return type 'RxInt'.
 - 'RxString' is from 'package:get/get_rx/src/rx_types/rx_types.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib/get_rx/src/rx_types/rx_types.dart').
 - 'RxInt' is from 'package:get/get_rx/src/rx_types/rx_types.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib/get_rx/src/rx_types/rx_types.dart').
  RxInt get videoId => _videoInfoState.videoId;
                                       ^
lib/pages/video_detail/controllers/video_detail_controller.dart:822:60: Error: The getter 'reportReasons' isn't defined for the class 'ReportService'.
 - 'ReportService' is from 'package:keepdance/pages/video_detail/services/report_service.dart' ('lib/pages/video_detail/services/report_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'reportReasons'.
  RxList<ReportReason> get reportReasons => _reportService.reportReasons;
                                                           ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:823:55: Error: The getter 'isLoadingReportReasons' isn't defined for the class 'ReportService'.
 - 'ReportService' is from 'package:keepdance/pages/video_detail/services/report_service.dart' ('lib/pages/video_detail/services/report_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoadingReportReasons'.
  RxBool get isLoadingReportReasons => _reportService.isLoadingReportReasons;
                                                      ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:846:47: Error: No named parameter with the name 'logger'.
      chartAnimationUtil = ChartAnimationUtil(logger: _logger);
                                              ^^^^^^
lib/pages/video_detail/utils/chart_animation_util.dart:9:7: Context: The class 'ChartAnimationUtil' has a constructor that takes no arguments.
class ChartAnimationUtil {
      ^
lib/pages/video_detail/controllers/video_detail_controller.dart:879:17: Error: No named parameter with the name 'title'.
                title: videoTitle,
                ^^^^^
lib/models/dance_video_detail.dart:94:3: Context: Found this candidate, but the arguments don't match.
  DanceVideoDetail();
  ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:853:33: Error: The getter 'isInitialized' isn't defined for the class 'InteractionState'.
 - 'InteractionState' is from 'package:keepdance/pages/video_detail/states/interaction_state.dart' ('lib/pages/video_detail/states/interaction_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isInitialized'.
          if (!interactionState.isInitialized) {
                                ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:857:51: Error: A value of type 'int' can't be assigned to a variable of type 'String'.
                  _videoInfoState.videoId.value = numericId;
                                                  ^
lib/pages/video_detail/controllers/video_detail_controller.dart:858:35: Error: The setter 'localVideoStringId' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing setter, or defining a setter or field named 'localVideoStringId'.
                  _videoInfoState.localVideoStringId = id;
                                  ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:861:52: Error: A value of type 'int' can't be assigned to a variable of type 'String'.
                   _videoInfoState.videoId.value = 0;
                                                   ^
lib/pages/video_detail/controllers/video_detail_controller.dart:862:36: Error: The setter 'localVideoStringId' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing setter, or defining a setter or field named 'localVideoStringId'.
                   _videoInfoState.localVideoStringId = "";
                                   ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:866:31: Error: The getter 'previousRoute' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'previousRoute'.
              _videoInfoState.previousRoute.value = args['from'] ?? '/main';
                              ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:867:31: Error: The getter 'isCommunity' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCommunity'.
              _videoInfoState.isCommunity.value = args['isCommunity'] ?? false;
                              ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:870:46: Error: The getter 'isCommunity' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCommunity'.
                isCommunity: _videoInfoState.isCommunity.value
                                             ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:868:36: Error: Too few positional arguments: 2 required, 0 given.
              interactionState.init(
                                   ^
lib/pages/video_detail/controllers/video_detail_controller.dart:888:22: Error: The method '_loadLocalVideoMirrorState' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named '_loadLocalVideoMirrorState'.
               await _loadLocalVideoMirrorState();
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:896:11: Error: The method '_notifyUpdate' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named '_notifyUpdate'.
          _notifyUpdate();
          ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:930:38: Error: Member not found: 'instance'.
          await PoseCameraController.instance.dispose();
                                     ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:943:47: Error: A value of type 'RxList<dynamic>' can't be assigned to a variable of type 'List<String>'.
 - 'RxList' is from 'package:get/get_rx/src/rx_types/rx_types.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib/get_rx/src/rx_types/rx_types.dart').
 - 'List' is from 'dart:core'.
      GlobalData.instance.localVideoPath = [].obs;
                                              ^
lib/pages/video_detail/controllers/video_detail_controller.dart:961:25: Error: The getter 'isLoading' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoading'.
        _videoInfoState.isLoading.value = true;
                        ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:965:29: Error: The getter 'isLoading' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoading'.
            _videoInfoState.isLoading.value = false;
                            ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:971:29: Error: The getter 'isCommunity' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCommunity'.
        if (_videoInfoState.isCommunity.value) {
                            ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:980:33: Error: The getter 'isCommunity' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCommunity'.
            if (_videoInfoState.isCommunity.value) {
                                ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:988:25: Error: The getter 'isLoading' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoading'.
        _videoInfoState.isLoading.value = false;
                        ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:991:25: Error: The getter 'isLoading' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoading'.
        _videoInfoState.isLoading.value = false;
                        ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1029:28: Error: The getter 'isCommunity' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isCommunity'.
       if (_videoInfoState.isCommunity.value) {
                           ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1049:63: Error: The argument type 'DanceVideoDetail' can't be assigned to the parameter type 'String'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
          await titleAnimationController.updateStatusBarColor(detail);
                                                              ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1065:62: Error: Member not found: 'DanceVideoDetail.fromCommunityDetail'.
        _videoInfoState.videoDetail.value = DanceVideoDetail.fromCommunityDetail(detail);
                                                             ^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1063:49: Error: A value of type '(CommunityDetail?, DanceVideoDetail?)' can't be assigned to a variable of type 'CommunityDetail?'.
 - 'CommunityDetail' is from 'package:keepdance/models/community_detail.dart' ('lib/models/community_detail.dart').
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
        _videoInfoState.communityDetail.value = detail;
                                                ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1070:25: Error: The getter 'isLoading' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoading'.
        _videoInfoState.isLoading.value = false;
                        ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1078:81: Error: The getter 'localVideoStringId' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'localVideoStringId'.
      var bestScore = await scoreService.getBestLocalVideoScore(_videoInfoState.localVideoStringId!);
                                                                                ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1080:60: Error: A value of type 'Map<String, dynamic>' can't be assigned to a variable of type 'DanceScoreDetail?'.
 - 'Map' is from 'dart:core'.
 - 'DanceScoreDetail' is from 'package:keepdance/models/dance_score_detail.dart' ('lib/models/dance_score_detail.dart').
        _videoInfoState.danceScoreDetail.value = bestScore.toDanceScoreDetailJson();
                                                           ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1100:28: Error: The getter 'organSort' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'organSort'.
           _videoInfoState.organSort.value = detail.organSort ?? "0";
                           ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1101:28: Error: The getter 'progressRank' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'progressRank'.
           _videoInfoState.progressRank.value = detail.progressRank ?? "0";
                           ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1101:56: Error: The getter 'progressRank' isn't defined for the class 'DanceScoreDetail'.
 - 'DanceScoreDetail' is from 'package:keepdance/models/dance_score_detail.dart' ('lib/models/dance_score_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'progressRank'.
           _videoInfoState.progressRank.value = detail.progressRank ?? "0";
                                                       ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1102:28: Error: The getter 'progressRate' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'progressRate'.
           _videoInfoState.progressRate.value = detail.progressRate ?? "0.0";
                           ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1102:56: Error: The getter 'progressRate' isn't defined for the class 'DanceScoreDetail'.
 - 'DanceScoreDetail' is from 'package:keepdance/models/dance_score_detail.dart' ('lib/models/dance_score_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'progressRate'.
           _videoInfoState.progressRate.value = detail.progressRate ?? "0.0";
                                                       ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1119:45: Error: A value of type 'String' can't be assigned to a variable of type 'int'.
      int videoId = _videoInfoState.videoId.value;
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1126:25: Error: The getter 'maxScoreSortList' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'maxScoreSortList'.
        _videoInfoState.maxScoreSortList.value = rankingData;
                        ^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1140:7: Error: The getter '_isLoadingLocalHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isLoadingLocalHistory'.
      _isLoadingLocalHistory.value = true;
      ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1150:15: Error: The method 'LateInitializationError' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'LateInitializationError'.
        throw LateInitializationError('_videoInfoState');
              ^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1154:98: Error: The getter 'localVideoStringId' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'localVideoStringId'.
      final historyList = await localVideoScoreService.getLocalVideoScoreHistory(_videoInfoState.localVideoStringId ?? '');
                                                                                          ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1160:7: Error: The getter '_localVideoHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoHistory'.
      _localVideoHistory.value = historyList;
      ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1163:7: Error: The getter '_localVideoStatistics' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoStatistics'.
      _localVideoStatistics.value = statistics;
      ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1176:7: Error: The getter '_localVideoHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoHistory'.
      _localVideoHistory.clear();
      ^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1177:7: Error: The getter '_localVideoStatistics' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_localVideoStatistics'.
      _localVideoStatistics.clear();
      ^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1181:7: Error: The getter '_isLoadingLocalHistory' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_isLoadingLocalHistory'.
      _isLoadingLocalHistory.value = false;
      ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1215:15: Error: The method 'LateInitializationError' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'LateInitializationError'.
        throw LateInitializationError('_videoInfoState');
              ^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1219:45: Error: A value of type 'String' can't be assigned to a variable of type 'int'.
      int videoId = _videoInfoState.videoId.value;
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1240:23: Error: The getter 'vipStatus' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'vipStatus'.
      _videoInfoState.vipStatus.value = status;
                      ^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1318:26: Error: The getter 'reportReasons' isn't defined for the class 'ReportService'.
 - 'ReportService' is from 'package:keepdance/pages/video_detail/services/report_service.dart' ('lib/pages/video_detail/services/report_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'reportReasons'.
      if (_reportService.reportReasons.isNotEmpty) {
                         ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1321:22: Error: The getter 'isLoadingReportReasons' isn't defined for the class 'ReportService'.
 - 'ReportService' is from 'package:keepdance/pages/video_detail/services/report_service.dart' ('lib/pages/video_detail/services/report_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoadingReportReasons'.
      _reportService.isLoadingReportReasons.value = true;
                     ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1325:30: Error: The getter 'reportReasons' isn't defined for the class 'ReportService'.
 - 'ReportService' is from 'package:keepdance/pages/video_detail/services/report_service.dart' ('lib/pages/video_detail/services/report_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'reportReasons'.
              _reportService.reportReasons.value = reasons;
                             ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1330:27: Error: The getter 'isLoadingReportReasons' isn't defined for the class 'ReportService'.
 - 'ReportService' is from 'package:keepdance/pages/video_detail/services/report_service.dart' ('lib/pages/video_detail/services/report_service.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLoadingReportReasons'.
           _reportService.isLoadingReportReasons.value = false;
                          ^^^^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1336:45: Error: Too many positional arguments: 0 allowed, but 6 found.
Try removing the extra positional arguments.
    return await _reportService.submitReport(reasonId, content, contact, images, type, targetId);
                                            ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1353:17: Error: The getter 'animationController' isn't defined for the class 'ChartAnimationUtil'.
 - 'ChartAnimationUtil' is from 'package:keepdance/pages/video_detail/utils/chart_animation_util.dart' ('lib/pages/video_detail/utils/chart_animation_util.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'animationController'.
        if(util.animationController == null) {
                ^^^^^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1358:55: Error: The getter 'currentTabIndex' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentTabIndex'.
        util.startAnimation(isSingle: _videoInfoState.currentTabIndex.value == 2);
                                                      ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1358:29: Error: No named parameter with the name 'isSingle'.
        util.startAnimation(isSingle: _videoInfoState.currentTabIndex.value == 2);
                            ^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1363:27: Error: The getter 'currentTabIndex' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentTabIndex'.
      if (_videoInfoState.currentTabIndex.value == 2 && index != 2) {
                          ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1366:23: Error: The getter 'currentTabIndex' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentTabIndex'.
      _videoInfoState.currentTabIndex.value = index;
                      ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1385:37: Error: The getter 'scoreHistory' isn't defined for the class 'VideoInfoState'.
 - 'VideoInfoState' is from 'package:keepdance/pages/video_detail/states/video_info_state.dart' ('lib/pages/video_detail/states/video_info_state.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'scoreHistory'.
    final history = _videoInfoState.scoreHistory.value;
                                    ^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1428:41: Error: Too few positional arguments: 8 required, 0 given.
    EventAnalytics.trackVideoButtonClick(
                                        ^
lib/utils/analytics/event_analytics.dart:264:15: Context: Found this candidate, but the arguments don't match.
  static void trackVideoButtonClick(
              ^^^^^^^^^^^^^^^^^^^^^
lib/pose/Controller/pose_camera_controller.dart:43:29: Error: The getter 'Deviceorientation' isn't defined for the class 'PoseCameraController'.
 - 'PoseCameraController' is from 'package:keepdance/pose/Controller/pose_camera_controller.dart' ('lib/pose/Controller/pose_camera_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'Deviceorientation'.
        deviceOrientation = Deviceorientation.portraitUp;
                            ^^^^^^^^^^^^^^^^^
lib/pose/Controller/pose_camera_controller.dart:69:46: Error: Member not found: 'camera'.
                    context, PermissionScene.camera);
                                             ^^^^^^
lib/pose/Controller/pose_camera_controller.dart:68:64: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
                await PermissionManager.requestCameraPermission(
                                                               ^
lib/utils/permission/permission_manager.dart:271:23: Context: Found this candidate, but the arguments don't match.
  static Future<bool> requestCameraPermission([PermissionScene scene = PermissionScene.other]) async {
                      ^^^^^^^^^^^^^^^^^^^^^^^
lib/pose/Controller/pose_camera_controller.dart:321:65: Error: The getter 'BufferState' isn't defined for the class 'PoseCameraController'.
 - 'PoseCameraController' is from 'package:keepdance/pose/Controller/pose_camera_controller.dart' ('lib/pose/Controller/pose_camera_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'BufferState'.
        _smartFrameRateController.recordBufferState(diff < 16 ? BufferState.full : BufferState.ok);
                                                                ^^^^^^^^^^^
lib/pose/Controller/pose_camera_controller.dart:321:84: Error: The getter 'BufferState' isn't defined for the class 'PoseCameraController'.
 - 'PoseCameraController' is from 'package:keepdance/pose/Controller/pose_camera_controller.dart' ('lib/pose/Controller/pose_camera_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'BufferState'.
        _smartFrameRateController.recordBufferState(diff < 16 ? BufferState.full : BufferState.ok);
                                                                                   ^^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart:22:23: Error: The method 'hashValues' isn't defined for the class 'DrmInitData'.
 - 'DrmInitData' is from 'package:better_player/src/hls/hls_parser/drm_init_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(schemeType, schemeData);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart:52:23: Error: The method 'hashValues' isn't defined for the class 'SchemeData'.
 - 'SchemeData' is from 'package:better_player/src/hls/hls_parser/scheme_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart:31:23: Error: The method 'hashValues' isn't defined for the class 'HlsTrackMetadataEntry'.
 - 'HlsTrackMetadataEntry' is from 'package:better_player/src/hls/hls_parser/hls_track_metadata_entry.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(groupId, name, variantInfos);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart:44:23: Error: The method 'hashValues' isn't defined for the class 'VariantInfo'.
 - 'VariantInfo' is from 'package:better_player/src/hls/hls_parser/variant_info.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:157:38: Error: Method not found: 'isWeChatInstalled'.
      bool isInstalled = await fluwx.isWeChatInstalled();
                                     ^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:205:50: Error: Undefined name 'WeChatImage'.
        thumbnail: thumbnailData != null ? fluwx.WeChatImage.binary(thumbnailData) : null,
                                                 ^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:200:68: Error: Too few positional arguments: 1 required, 0 given.
      fluwx.WeChatShareFileModel model = fluwx.WeChatShareFileModel(
                                                                   ^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fluwx-5.7.2/lib/src/foundation/share_models.dart:309:3: Context: Found this candidate, but the arguments don't match.
  WeChatShareFileModel(
  ^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:211:26: Error: Undefined name 'weChatResponseEventHandler'.
      subscriber = fluwx.weChatResponseEventHandler.distinct().listen((fluwx.WeChatShareResponse response) {
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:221:19: Error: Method not found: 'shareToWeChat'.
      await fluwx.shareToWeChat(model);
                  ^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:383:19: Error: Method not found: 'registerWxApi'.
      await fluwx.registerWxApi(
                  ^^^^^^^^^^^^^
lib/pages/home/<USER>/home_controller.dart:190:13: Error: No named parameter with the name 'vipExpireTime'.
            vipExpireTime: userInfo.value?.vipExpireTime,
            ^^^^^^^^^^^^^
lib/utils/analytics/base_analytics.dart:48:23: Context: Found this candidate, but the arguments don't match.
  static Future<void> updateUserInfo({
                      ^^^^^^^^^^^^^^
lib/pages/home/<USER>/home_controller.dart:226:56: Error: The argument type 'DateTime' can't be assigned to the parameter type 'String?'.
 - 'DateTime' is from 'dart:core'.
        await VipCacheService.inferVipLevelFromEndTime(vipExpireTime, userId);
                                                       ^
lib/pages/home/<USER>/home_controller.dart:249:69: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
        final response = await _apiClient.post<Map<String, dynamic>>(
                                                                    ^
lib/pages/home/<USER>/home_controller.dart:291:75: Error: The getter 'name' isn't defined for the class 'DanceTypeChild'.
 - 'DanceTypeChild' is from 'package:keepdance/models/dance_type.dart' ('lib/models/dance_type.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'name'.
        recommendChildren.add(TypeItemDataChild(id: child.id, name: child.name));
                                                                          ^^^^
lib/pages/home/<USER>/home_controller.dart:294:76: Error: The getter 'name' isn't defined for the class 'DanceType'.
 - 'DanceType' is from 'package:keepdance/models/dance_type.dart' ('lib/models/dance_type.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'name'.
    newTypeList.add(TypeItemData(id: recommendType.id, name: recommendType.name, children: recommendChildren));
                                                                           ^^^^
lib/pages/home/<USER>/home_controller.dart:299:70: Error: The getter 'name' isn't defined for the class 'DanceType'.
 - 'DanceType' is from 'package:keepdance/models/dance_type.dart' ('lib/models/dance_type.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'name'.
        newTypeList.add(TypeItemData(id: typeData.id, name: typeData.name, children: <TypeItemDataChild>[].obs));
                                                                     ^^^^
lib/pages/home/<USER>/home_controller.dart:309:63: Error: Too many positional arguments: 0 allowed, but 1 found.
Try removing the extra positional arguments.
      final themes = await _danceThemeService.fetchDanceThemes(headers);
                                                              ^
lib/pages/home/<USER>/home_controller.dart:351:76: Error: Too few positional arguments: 2 required, 0 given.
        final newHistory = await _danceHistoryService.fetchUserDanceHistory(
                                                                           ^
lib/pages/home/<USER>/home_controller.dart:417:59: Error: The method 'fetchJustDanceMaterialList' isn't defined for the class 'JustDanceCategoryService'.
 - 'JustDanceCategoryService' is from 'package:keepdance/pages/home/<USER>/just_dance_category_service.dart' ('lib/pages/home/<USER>/just_dance_category_service.dart').
Try correcting the name to the name of an existing method, or defining a method named 'fetchJustDanceMaterialList'.
        final materials = await _justDanceCategoryService.fetchJustDanceMaterialList(
                                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/home/<USER>/home_controller.dart:494:50: Error: Too many positional arguments: 3 allowed, but 4 found.
Try removing the extra positional arguments.
    await _errorClassificationService.handleError(
                                                 ^
lib/services/device_info_service.dart:137:39: Error: The getter 'isLowRamDevice' isn't defined for the class 'AndroidDeviceInfo'.
 - 'AndroidDeviceInfo' is from 'package:device_info_plus/src/model/android_device_info.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/device_info_plus-9.1.2/lib/src/model/android_device_info.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'isLowRamDevice'.
        'isLowRamDevice': androidInfo.isLowRamDevice.toString(),
                                      ^^^^^^^^^^^^^^
lib/pages/creation/services/vip_quota_service.dart:39:28: Error: The getter 'vipInfo' isn't defined for the class 'User'.
 - 'User' is from 'package:keepdance/models/user.dart' ('lib/models/user.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'vipInfo'.
      final vipInfo = user.vipInfo;
                           ^^^^^^^
lib/pages/creation/services/pagination_service.dart:269:69: Error: The argument type 'String?' can't be assigned to the parameter type 'String' because 'String?' is nullable and 'String' isn't.
      data = data.where((item) => PinyinSearchUtil.matchSearch(item.title, _searchKeyword));
                                                                    ^
lib/pose/utils/pose_plugin_manager.dart:32:46: Error: A constructor invocation can't have type arguments after the constructor name.
Try removing the type arguments or placing them after the class name.
    _poseResultController = StreamController.broadcast<List<List<PoseJoint>>>();
                                             ^^^^^^^^^
lib/pose/utils/pose_plugin_manager.dart:110:19: Error: The method 'setPoseResultHandler' isn't defined for the class 'PosePlugin'.
 - 'PosePlugin' is from 'package:keepdance/pose/utils/pose_plugin.dart' ('lib/pose/utils/pose_plugin.dart').
Try correcting the name to the name of an existing method, or defining a method named 'setPoseResultHandler'.
      _posePlugin.setPoseResultHandler(_handlePoseResult);
                  ^^^^^^^^^^^^^^^^^^^^
lib/pose/utils/pose_plugin_manager.dart:212:17: Error: The method 'resetHandler' isn't defined for the class 'PosePlugin'.
 - 'PosePlugin' is from 'package:keepdance/pose/utils/pose_plugin.dart' ('lib/pose/utils/pose_plugin.dart').
Try correcting the name to the name of an existing method, or defining a method named 'resetHandler'.
    _posePlugin.resetHandler();
                ^^^^^^^^^^^^
lib/pose/utils/pose_plugin.dart:69:16: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
      _logger.i('姿势检测插件: 开始初始化检测器', {
               ^
lib/pose/utils/pose_plugin.dart:85:16: Error: Too many positional arguments: 1 allowed, but 3 found.
Try removing the extra positional arguments.
      _logger.e('姿势检测插件: 检测器初始化失败', error, stackTrace);
               ^
lib/pose/utils/pose_plugin.dart:99:20: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
          _logger.e(
                   ^
lib/pose/utils/pose_plugin.dart:138:16: Error: Too many positional arguments: 1 allowed, but 3 found.
Try removing the extra positional arguments.
      _logger.e('姿势检测插件: 检测结果解析失败', error, stackTrace);
               ^
lib/pose/utils/pose_plugin.dart:184:16: Error: Too many positional arguments: 1 allowed, but 3 found.
Try removing the extra positional arguments.
      _logger.e('姿势检测插件: 图像数据发送失败', error, stackTrace);
               ^
lib/pose/utils/pose_plugin.dart:233:16: Error: Too many positional arguments: 1 allowed, but 3 found.
Try removing the extra positional arguments.
      _logger.e('姿势检测插件: 图像数据发送失败: $error', error, stackTrace);
               ^
lib/pages/creation/views/widgets/creation/empty_state_widget.dart:109:26: Error: The getter 'HapticFeedback' isn't defined for the class '_EmptyStateWidgetState'.
 - '_EmptyStateWidgetState' is from 'package:keepdance/pages/creation/views/widgets/creation/empty_state_widget.dart' ('lib/pages/creation/views/widgets/creation/empty_state_widget.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'HapticFeedback'.
                         HapticFeedback.mediumImpact();
                         ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/empty_state_widget.dart:169:26: Error: The getter 'HapticFeedback' isn't defined for the class '_EmptyStateWidgetState'.
 - '_EmptyStateWidgetState' is from 'package:keepdance/pages/creation/views/widgets/creation/empty_state_widget.dart' ('lib/pages/creation/views/widgets/creation/empty_state_widget.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'HapticFeedback'.
                         HapticFeedback.mediumImpact();
                         ^^^^^^^^^^^^^^
lib/pages/creation/views/widgets/creation/empty_state_widget.dart:376:23: Error: Method not found: 'Random'.
    final random = ui.Random(153);
                      ^^^^^^
lib/utils/analytics/event_analytics.dart:16:52: Error: Member not found: 'logger'.
  static late final Logger _logger = BaseAnalytics.logger;
                                                   ^^^^^^
lib/utils/analytics/event_analytics.dart:29:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, ['action_type'])) {
                                                      ^
lib/utils/analytics/event_analytics.dart:153:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, requiredKeys)) {
                                                      ^
lib/utils/analytics/event_analytics.dart:201:34: Error: Method not found: 'mapToString'.
      params['copyright_info'] = mapToString(copyrightInfo);
                                 ^^^^^^^^^^^
lib/utils/analytics/event_analytics.dart:229:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, ['parent_tab', 'category_name'])) {
                                                      ^
lib/utils/analytics/event_analytics.dart:478:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, ['keyword'])) {
                                                      ^
lib/utils/analytics/event_analytics.dart:491:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, ['search_keyword', 'search_type'])) {
                                                      ^
lib/utils/analytics/event_analytics.dart:530:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, ['input_length', 'is_valid_format', 'source_page'])) {
                                                      ^
lib/utils/analytics/event_analytics.dart:557:57: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
      if (!BaseAnalytics.validateRequiredParams(params, ['from_package', 'to_package', 'original_price', 'current_price'])) {
                                                        ^
lib/utils/analytics/event_analytics.dart:576:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, ['from_tab', 'to_tab'])) {
                                                      ^
lib/utils/analytics/event_analytics.dart:610:55: Error: The argument type 'List<String>' can't be assigned to the parameter type 'Set<String>'.
 - 'List' is from 'dart:core'.
 - 'Set' is from 'dart:core'.
    if (!BaseAnalytics.validateRequiredParams(params, requiredKeys)) {
                                                      ^
lib/pages/video_detail/utils/video_preferences.dart:51:41: Error: Member not found: 'landscape'.
        orElse: () => ScreenOrientation.landscape,
                                        ^^^^^^^^^
lib/pages/video_detail/utils/video_preferences.dart:71:42: Error: Member not found: 'landscape'.
        'orientation': ScreenOrientation.landscape,
                                         ^^^^^^^^^
lib/pages/video_detail/services/video_detail_service.dart:36:73: Error: Expected ':' before this.
      final userId = (userData is Map) ? userData['value']?['id'] : null;
                                                                        ^
lib/pages/video_detail/services/video_detail_service.dart:36:73: Error: Expected an identifier, but got ';'.
Try inserting an identifier before ';'.
      final userId = (userData is Map) ? userData['value']?['id'] : null;
                                                                        ^
lib/pages/video_detail/services/video_detail_service.dart:65:73: Error: Expected ':' before this.
      final userId = (userData is Map) ? userData['value']?['id'] : null;
                                                                        ^
lib/pages/video_detail/services/video_detail_service.dart:65:73: Error: Expected an identifier, but got ';'.
Try inserting an identifier before ';'.
      final userId = (userData is Map) ? userData['value']?['id'] : null;
                                                                        ^
lib/pages/video_detail/services/video_detail_service.dart:191:11: Error: No named parameter with the name 'id'.
          id: videoInfo?.id,
          ^^
lib/models/dance_video_detail.dart:94:3: Context: Found this candidate, but the arguments don't match.
  DanceVideoDetail();
  ^^^^^^^^^^^^^^^^
lib/pages/video_detail/services/video_detail_service.dart:189:43: Error: The getter 'videoInfo' isn't defined for the class 'CommunityDetail'.
 - 'CommunityDetail' is from 'package:keepdance/models/community_detail.dart' ('lib/models/community_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'videoInfo'.
        final videoInfo = communityDetail.videoInfo;
                                          ^^^^^^^^^
lib/pages/video_detail/services/video_detail_service.dart:244:73: Error: Expected ':' before this.
      final userId = (userData is Map) ? userData['value']?['id'] : null;
                                                                        ^
lib/pages/video_detail/services/video_detail_service.dart:244:73: Error: Expected an identifier, but got ';'.
Try inserting an identifier before ';'.
      final userId = (userData is Map) ? userData['value']?['id'] : null;
                                                                        ^
lib/pages/video_detail/services/report_service.dart:27:45: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
      final response = await _apiClient.post(
                                            ^
lib/pages/video_detail/services/report_service.dart:88:45: Error: Too many positional arguments: 1 allowed, but 2 found.
Try removing the extra positional arguments.
      final response = await _apiClient.post(
                                            ^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:637:12: Error: The method 'hashValues' isn't defined for the class 'PaletteTarget'.
 - 'PaletteTarget' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(
           ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:856:12: Error: The method 'hashValues' isn't defined for the class 'PaletteColor'.
 - 'PaletteColor' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(color, population);
           ^^^^^^^^^^
lib/pages/creation/views/widgets/creation/action_button.dart:66:11: Error: No named parameter with the name 'direction'.
          direction: Axis.vertical, // 默认值
          ^^^^^^^^^
/D:/Program%20Files/Flutterdev/flutter/packages/flutter/lib/src/widgets/basic.dart:5356:9: Context: Found this candidate, but the arguments don't match.
  const Column({
        ^^^^^^
lib/pages/creation/views/widgets/creation/data_restoring_banner.dart:137:31: Error: The getter '_controller' isn't defined for the class 'DataRestoringBanner'.
 - 'DataRestoringBanner' is from 'package:keepdance/pages/creation/views/widgets/creation/data_restoring_banner.dart' ('lib/pages/creation/views/widgets/creation/data_restoring_banner.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named '_controller'.
        final progressValue = _controller.progress.value / 100.0;
                              ^^^^^^^^^^^
Target kernel_snapshot_program failed: Exception


FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileFlutterBuildDebug'.
> Process 'command 'D:\Program Files\Flutterdev\flutter\bin\flutter.bat'' finished with non-zero exit value 1

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 6s
Running Gradle task 'assembleDebug'...                              6.2s
Error: Gradle task assembleDebug failed with exit code 1