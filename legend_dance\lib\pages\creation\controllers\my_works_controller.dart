// lib: package:keepdance/pages/creation/controllers/my_works_controller.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keepdance/common_widgets/vip_upgrade_dialog.dart'; // 假设路径
import 'package:keepdance/models/dance_category.dart'; // 假设路径
import 'package:keepdance/models/user.dart'; // 假设路径
import 'package:keepdance/models/work_item_data.dart'; // 假设路径
import 'package:keepdance/pages/creation/services/creation_preload_service.dart'; // 假设路径
import 'package:keepdance/pages/creation/services/image_optimization_service.dart'; // 假设路径
import 'package:keepdance/pages/creation/services/my_works_service.dart' as services; // 假设路径
import 'package:keepdance/pages/creation/services/pagination_service.dart'; // 假设路径
import 'package:keepdance/pages/creation/utils/dance_import_utils.dart'; // 假设路径
import 'package:keepdance/pages/home/<USER>/home_controller.dart'; // 假设路径
import 'package:keepdance/services/data_sync_service.dart'; // 假设路径
import 'package:keepdance/services/local_database_service.dart'; // 假设路径
import '../services/vip_quota_service.dart'; // 假设路径
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

// --- 占位符定义 ---
// 注意：这些是根据控制器中的用法推断出的基本结构，实际项目中它们会有更完整的实现。
class VipLevel {
  static const free = 'free';
  static const monthly = 'monthly';
  static const quarterly = 'quarterly';
  static const yearly = 'yearly';
}
// --- 占位符定义结束 ---

/// 我的作品页面控制器
class MyWorksController extends GetxController {
  //region 依赖服务
  late final services.MyWorksService _myWorksService;
  late final PaginationService _paginationService;
  late final LocalDatabaseService _localDatabaseService;
  late final DataSyncService _dataSyncService;
  late final CreationPreloadService _preloadService;
  final ImageOptimizationService _imageOptimizationService = Get.find();
  //endregion

  //region 内部状态和工具
  final Logger _logger = Logger();
  /// 所有作品的主列表，作为唯一数据源。
  final List<WorkItemData> _allWorks = <WorkItemData>[];
  Timer? _searchDebounce;
  //endregion

  //region 响应式状态 (UI State)
  /// UI显示的作品列表，通过分页服务从 [_allWorks] 派生。
  final RxList<WorkItemData> worksForUI = <WorkItemData>[].obs;
  /// 多选模式下选中的作品ID集合。
  final RxSet<String> selectedWorkIds = <String>{}.obs;
  /// 是否处于多选模式。
  final RxBool isMultiSelectMode = false.obs;
  /// 当前的舞种分类筛选器。
  final Rx<DanceCategoryType?> currentFilter = Rx<DanceCategoryType?>(null);
  /// 基于当前作品列表动态生成的可用舞种分类。
  final RxList<DanceCategoryType> availableCategories = <DanceCategoryType>[].obs;
  /// 是否显示首次使用AI工坊的提示。
  final RxBool shouldShowFirstTimeTip = false.obs;
  final RxBool firstTimeTipShown = false.obs;
  /// 是否通过标注流程创建过作品。
  final RxBool hasCreatedWorkThroughAnnotation = false.obs;
  /// 是否显示导入选项。
  final RxBool showImportOptions = false.obs;
  /// 用户VIP状态。
  final RxBool isVip = false.obs;
  final RxBool isProVip = false.obs;
  /// 搜索功能状态。
  final RxString searchKeyword = ''.obs;
  final RxBool isSearchExpanded = false.obs;
  /// 创作介绍弹窗状态。
  final RxBool showCreationIntro = false.obs;
  final RxBool creationIntroShown = false.obs;
  /// 加载状态。
  final RxBool isInitialLoading = true.obs;
  final RxString loadingText = "内容加载中".obs;
  final RxBool isRecovering = false.obs;
  final RxString recoveringText = "数据恢复中，请稍候...".obs;
  /// 分页状态。
  final RxInt totalWorksCount = 0.obs;
  final RxInt serverTotalCount = 0.obs;
  final RxInt changeCount = 0.obs;
  final RxBool hasMore = true.obs;
  final RxString currentFilterKey = 'all'.obs;
  final RxBool isFetchingMoreData = false.obs;
  final TextEditingController searchController = TextEditingController();
  final RxString searchText = ''.obs;
  final RxBool isSearching = false.obs;
  final RxBool isEditing = false.obs;
  final Rxn<WorkItemData> currentWorkItem = Rxn<WorkItemData>();
  final RxList<String> selectedItemIds = <String>[].obs;
  final RxList<WorkItemData> worklistData = <WorkItemData>[].obs;
  final RxList<WorkItemData> workList = <WorkItemData>[].obs;
  final RxSet<String> selectedIdSet = <String>{}.obs;
  final RxList<WorkItemData> selectedWorks = <WorkItemData>[].obs;
  //endregion

  //region Getters
  /// 是否应显示搜索栏。
  bool get shouldShowSearchBar => isInitialLoading.isFalse && _allWorks.length > 8;
  
  /// 当前分类筛选器。
  Rx<DanceCategoryType?> get currentCategory => currentFilter;
  
  /// 当前选中的分类ID。
  String get selectedCategoryId => currentFilter.value?.id ?? 'all';
  
  /// 是否可以删除作品。
  RxBool get canDeleteWorks => isVip;
  
  /// 是否应显示创作介绍弹窗。
  RxBool get shouldShowCreationIntroDialog => showCreationIntro;
  
  /// 是否正在加载。
  RxBool get isLoading => isInitialLoading;
  
  /// 作品列表。
  RxList<WorkItemData> get works => worksForUI;
  
  /// 是否正在获取更多数据。
  RxBool get isFetchingMore => isFetchingMoreData;
  
  /// 舞蹈分类列表。
  RxList<DanceCategoryType> get danceCategoryList => availableCategories;
  
  /// 浮动操作按钮的Key。
  GlobalKey get floatingActionButtonKey => GlobalKey();
  //endregion

  //region 生命周期
  @override
  void onInit() {
    super.onInit();
    _initializeDependencies();
    _setupUserStateListener();
    _initializeWorksData();
  }

  @override
  void onClose() {
    _searchDebounce?.cancel();
    super.onClose();
  }
  //endregion

  //region 初始化方法
  /// 初始化依赖注入的服务。
  void _initializeDependencies() {
    _logger.d("初始化MyWorksController依赖服务");
    _myWorksService = Get.find<services.MyWorksService>();
    _localDatabaseService = Get.find<LocalDatabaseService>();
    _dataSyncService = Get.find<DataSyncService>();
    _preloadService = Get.find<CreationPreloadService>();
    _paginationService = PaginationService();
    _logger.d("MyWorksController依赖服务初始化完成");
  }

  /// 设置用户状态监听器，以便在用户登录/登出或VIP状态变化时更新UI。
  void _setupUserStateListener() {
    if (Get.isRegistered<HomeController>()) {
      final homeController = Get.find<HomeController>();
      if (homeController.userInfo.value != null) {
        _updateUserVipStatus();
      }
      ever<User?>(homeController.userInfo, (user) async {
        if (user != null) {
          await _updateUserVipStatus();
        } else {
          isVip.value = false;
          isProVip.value = false;
        }
      });
    } else {
      // 如果HomeController未注册，延迟后重试
      Future.delayed(const Duration(milliseconds: 500), () {
        _setupUserStateListener();
      });
    }
  }

  /// 初始化作品数据，包括从本地加载、状态更新等。
  Future<void> _initializeWorksData() async {
    isInitialLoading.value = true;
    isRecovering.value = false;
    loadingText.value = '正在准备数据...';
    changeCount.value = 0;
    serverTotalCount.value = 0;
    hasMore.value = true;

    try {
      await _loadFirstTimeTipStatus();
      await _loadCreationIntroStatus();
      await _loadAnnotationCreatedStatus();

      await Future.delayed(const Duration(milliseconds: 200));

      loadingText.value = '正在扫描本地作品...';
      final preloadResult = await _preloadService.loadWorksWithOfflineFirst();
      final works = preloadResult.works;
      
      loadingText.value = preloadResult.message;
      _allWorks.clear();
      _allWorks.addAll(works);

      if (_allWorks.isNotEmpty) {
        loadingText.value = '正在整理分类数据...';
        _setupDataUpdateListener();
        await _paginationService.initializeData(_allWorks);
        _syncPaginationToUI();
        _updateAvailableCategories();
        await Future.delayed(const Duration(milliseconds: 300));
      }
      
      loadingText.value = '即将完成...';
      _updateFirstTimeTipVisibility();
      _updateCreationIntroVisibility();
      await _updateUserVipStatus();
      
      currentFilterKey.value = _getCategoryKey(currentFilter.value);
      
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e, s) {
      _logger.e("初始化作品数据失败: $e");
      loadingText.value = '加载失败，请重试';
    } finally {
      isInitialLoading.value = false;
      isRecovering.value = false;
      loadingText.value = '';
    }
  }
  //endregion

  //region 作品操作 (CRUD)
  /// 删除单个作品。
  Future<void> deleteItem(WorkItemData item) async {
    await _deleteWorkItem(item);
  }

  Future<void> _deleteWorkItem(WorkItemData item) async {
    try {
      _logger.i('🚀 MyWorksController开始删除作品: ${item.title} (ID: ${item.id})');
      
      bool success = await _myWorksService.deleteWorkItem(item);

      if (success) {
        _allWorks.removeWhere((workItem) => workItem.id == item.id);
        _logger.d('✅ 作品已从主列表移除: ${item.id}');
        
        try {
          await _dataSyncService.syncDeleteWorkItem(item.id);
          _logger.d('✅ 数据库同步删除完成: ${item.id}');
        } catch (e) {
          _logger.w('同步删除到数据库失败: $e');
        }

        await _syncDataAndApplyFilters();
        
        // 确保UI列表也同步移除
        if (worksForUI.any((element) => element.id == item.id)) {
          _logger.w('⚠️ 警告：作品删除后仍在UI列表中，强制移除');
          worksForUI.removeWhere((element) => element.id == item.id);
        }

        _logger.i('🎉 MyWorksController删除操作完全成功: ${item.title} (ID: ${item.id})');

      } else {
        _logger.e('❌ 删除作品"${item.title}"失败：服务层返回失败');
      }
    } catch (e, s) {
      _logger.e('❌ 删除作品"${item.title}"失败: $e');
    }
  }

  /// 更新作品信息。
  Future<bool> updateWorkItem(WorkItemData item) async {
    final index = _allWorks.indexWhere((element) => element.id == item.id);
    if (index == -1) {
      _logger.w("未找到要更新的作品: ${item.id}");
      return false;
    }

    try {
      final success = await _myWorksService.updateWorkItem(item, {});
      if (success) {
        final oldItem = _allWorks[index];
        _allWorks[index] = item;
        try {
          await _dataSyncService.syncSingleWorkItem(item);
        } catch (e) {
          _logger.w("同步更新到数据库失败: $e");
        }
        await _syncDataAndApplyFilters();
        _logger.i("作品信息更新完成: ${oldItem.title} -> ${item.title}");
        return true;
      } else {
        _logger.e("更新作品信息失败: 服务层返回失败");
        return false;
      }
    } catch(e, s) {
      _logger.e("更新作品信息失败: $e");
      return false;
    }
  }
  
  /// 批量删除选中的作品。
  Future<void> deleteSelected() async {
    final idsToDelete = Set<String>.from(selectedWorkIds);
    selectedWorkIds.clear();
    toggleMultiSelectMode();

    for (final id in idsToDelete) {
      final itemToDelete = _allWorks.firstWhereOrNull((item) => item.id == id);
      if (itemToDelete != null) {
        await _deleteWorkItem(itemToDelete);
      }
    }
  }
  //endregion

  //region UI交互与状态更新
  /// 切换多选模式。
  void toggleMultiSelectMode([dynamic _]) {
    isMultiSelectMode.value = !isMultiSelectMode.value;
    if (isMultiSelectMode.isFalse) {
      selectedWorkIds.clear();
      if (isSearchExpanded.isTrue) {
        clearSearch();
      }
    }
  }

  /// 切换单个作品的选中状态。
  void toggleItemSelection(String workId) {
    if (selectedWorkIds.contains(workId)) {
      selectedWorkIds.remove(workId);
    } else {
      selectedWorkIds.add(workId);
    }
  }

  /// 全选或取消全选。
  void selectAll([dynamic _]) {
    if (selectedWorkIds.length == worksForUI.length) {
      selectedWorkIds.clear();
    } else {
      selectedWorkIds.addAll(worksForUI.map((item) => item.id).where((id) => id != null).cast<String>());
    }
  }

  /// 切换导入选项的可见性。
  void toggleImportOptions() {
    showImportOptions.value = !showImportOptions.value;
  }
  
  /// 切换搜索栏的展开/折叠状态。
  void toggleSearchExpanded() {
    isSearchExpanded.value = !isSearchExpanded.value;
    if (isSearchExpanded.isFalse) {
      clearSearch();
    }
  }

  /// 更改分类筛选器。
  Future<void> changeFilter(DanceCategoryType? newFilter) async {
    if (currentFilter == newFilter) return;

    currentFilter.value = newFilter;
    currentFilterKey.value = _getCategoryKey(newFilter);
    await _syncDataAndApplyFilters();
  }

  /// 刷新数据。
  Future<void> refreshData([dynamic _]) async {
    isInitialLoading.value = true;
    changeCount.value = 0;
    serverTotalCount.value = 0;
    hasMore.value = true;

    try {
      final preloadResult = await _preloadService.refreshWorksWithOfflineFirst();
      final works = preloadResult.works;
      
      loadingText.value = preloadResult.message;
      _allWorks.clear();
      _allWorks.addAll(works);

      await _paginationService.initializeData(_allWorks);
      _syncPaginationToUI();
      _updateFirstTimeTipVisibility();
      await _updateUserVipStatus();
    } catch (e, s) {
      _logger.e('刷新数据失败: $e');
    } finally {
      isInitialLoading.value = false;
    }
  }

  /// 刷新VIP状态。
  Future<void> refreshVipStatus() async {
    await _updateUserVipStatus();
  }
  //endregion

  //region 搜索与筛选
  /// 根据关键字搜索作品。
  void searchWorks(String keyword) {
    _searchDebounce?.cancel();
    _searchDebounce = Timer(const Duration(milliseconds: 300), () async {
      searchKeyword.value = keyword;
      final useDatabase = await _shouldUseDatabaseCache();
      if (useDatabase && keyword.trim().isNotEmpty) {
        await _searchFromDatabase(keyword);
      } else {
        await _paginationService.setSearchKeyword(keyword);
        _syncPaginationToUI();
        _updateAvailableCategories();
      }
    });
  }

  /// 从数据库中搜索。
  Future<void> _searchFromDatabase(String keyword) async {
    loadingText.value = "搜索中...";
    isInitialLoading.value = true;

    try {
      final categoryId = currentFilter.value?.id;
      final results = await _localDatabaseService.getWorkItems(
        categoryId: categoryId,
        searchKeyword: keyword.trim(),
        limit: 1000,
      );
      _allWorks.clear();
      _allWorks.addAll(results);
      totalWorksCount.value = results.length;
      serverTotalCount.value = results.length;
      hasMore.value = false;
      await _syncDataAndApplyFilters();
    } catch (e, s) {
      _logger.e("数据库搜索失败: $e");
      await _paginationService.setSearchKeyword(keyword); // Fallback
      _syncPaginationToUI();
    } finally {
      isInitialLoading.value = false;
    }
  }

  /// 清除搜索状态。
  void clearSearch() {
    _searchDebounce?.cancel();
    searchKeyword.value = '';
    _paginationService.setSearchKeyword('').then((_) {
      _syncPaginationToUI();
      _updateAvailableCategories();
      _logger.d("搜索已安全清空，UI状态已同步");
    }).catchError((e) {
      _logger.e("清空搜索失败: $e");
      // 即使失败也要尝试恢复UI
      _syncPaginationToUI();
      _updateAvailableCategories();
    });
  }
  //endregion

  //region 数据同步与更新
  /// 将分页服务的数据同步到UI。
  void _syncPaginationToUI() {
    worksForUI.assignAll(_paginationService.pagedData);
    totalWorksCount.value = _paginationService.totalCount;
    serverTotalCount.value = _paginationService.serverTotalCount;
    hasMore.value = _paginationService.hasMore;
    changeCount.value = _paginationService.changeCount;
  }

  /// 同步数据并应用筛选器。
  Future<void> _syncDataAndApplyFilters() async {
    await _paginationService.updateData(_allWorks);
    await _paginationService.setFilter(currentFilter.value);
    _syncPaginationToUI();
    _updateAvailableCategories();
    await _updateUserVipStatus();
  }

  /// 更新可用的舞种分类列表。
  void _updateAvailableCategories() {
    final newCategories = getAvailableCategories();
    availableCategories.clear();
    availableCategories.addAll(newCategories);

    if (currentFilter.value != null && !newCategories.contains(currentFilter.value)) {
      _logger.w('当前筛选分类 ${currentFilter.value?.name} 不在可用列表中，重置为"全部"');
      currentFilter.value = null;
    }
  }

  /// 从所有作品中提取可用的舞种分类。
  List<DanceCategoryType> getAvailableCategories() {
    final categoryIds = <String>{};
    for (var work in _allWorks) {
      String? id = work.categoryId ?? work.category?['id']?.toString();
      if (id != null && id.isNotEmpty) {
        categoryIds.add(id);
      } else {
        // 默认或无分类
        categoryIds.add('1'); 
      }
    }

    final categories = DanceCategoryType.values
        .where((cat) => categoryIds.contains(cat.id))
        .toList();
    
    // 确保“全部”选项始终存在
    if (categories.isNotEmpty) {
      categories.insert(0, DanceCategoryType.all);
    }

    return categories;
  }
  
  /// 数据更新监听器 (目前已禁用)。
  void _setupDataUpdateListener() {
    _logger.d("数据更新监听已禁用（后台同步已优化移除）");
  }
  //endregion

  //region VIP 与权限
  /// 更新用户VIP状态。
  Future<void> _updateUserVipStatus() async {
    try {
      if (!Get.isRegistered<HomeController>()) {
        isVip.value = false;
        isProVip.value = false;
        return;
      }
      final homeController = Get.find<HomeController>();
      if (homeController.currentUser == null) {
        isVip.value = false;
        isProVip.value = false;
        return;
      }

      final vipLevel = await VipQuotaService.getCurrentVipLevel();
      isVip.value = (vipLevel != VipLevel.free);
      isProVip.value = (vipLevel == VipLevel.quarterly || vipLevel == VipLevel.yearly);
    } catch (e, s) {
      _logger.e("更新用户VIP状态失败: $e");
      isVip.value = false;
      isProVip.value = false;
    }
  }

  /// 检查批量删除权限，如果权限不足则显示升级弹窗。
  Future<bool> checkDeletePermissionAndShowUpgrade() async {
    if (isVip.value) return true;

    final vipLevel = await VipQuotaService.getCurrentVipLevel();
    if (vipLevel != VipLevel.free) {
      final quota = vipLevel == VipLevel.monthly ? 20 : (vipLevel == VipLevel.quarterly ? 40 : -1);
      final levelName = vipLevel == VipLevel.monthly ? "月会员" : (vipLevel == VipLevel.quarterly ? "季会员" : "年会员");
      
      _logger.i('$levelName达到配额上限(${_allWorks.length}/$quota)，显示年会员升级弹窗');
      final message = '您的$levelName作品空间已满（$quota个作品）。\n升级到年会员，享受无限作品空间，随心创作！';
      await VipUpgradeDialog.showGeneralUpgradeDialog(message);
      return false;
    } else {
      _logger.i("免费用户尝试删除作品，显示VIP升级弹窗");
      final config = VipUpgradeDialogConfig(/* ... */); // 配置细节无法从汇编还原
      await VipUpgradeDialog.showFeatureUpgradeDialog(config);
      return false;
    }
  }

  /// 检查分享权限。
  Future<bool> checkSharePermissionAndShowUpgrade() async {
    if (isProVip.value) return true;

    _logger.i("免费用户尝试分享作品，显示VIP升级弹窗");
    final config = VipUpgradeDialogConfig(/* ... */);
    await VipUpgradeDialog.showFeatureUpgradeDialog(config);
    return false;
  }
  //endregion

  //region 首次引导与标记
  /// 更新首次提示的可见性。
  void _updateFirstTimeTipVisibility() {
    shouldShowFirstTimeTip.value = !firstTimeTipShown.value && !hasCreatedWorkThroughAnnotation.value;
  }
  
  /// 更新创作介绍弹窗的可见性。
  void _updateCreationIntroVisibility() {
    showCreationIntro.value = !creationIntroShown.value;
  }

  /// 用户点击了"知道了"等按钮，标记首次提示已显示。
  Future<void> showFirstTimeTip() async {
    _logger.i("用户点击首次提示按钮");
    await _saveFirstTimeTipStatus();
    shouldShowFirstTimeTip.value = false;
  }

  /// 标记创作介绍弹窗已显示。
  Future<void> markCreationIntroShown() async {
    _logger.i("用户已查看创作介绍弹窗");
    await _saveCreationIntroStatus();
    showCreationIntro.value = false;
  }
  
  /// 标记用户通过标注流程创建了作品。
  Future<void> markWorkCreatedThroughAnnotation() async {
    _logger.i("标记用户已通过标注流程创建作品");
    await _saveAnnotationCreatedStatus();
    _updateFirstTimeTipVisibility();
  }
  //endregion

  //region 持久化状态 (SharedPreferences)
  Future<void> _loadFirstTimeTipStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      firstTimeTipShown.value = prefs.getBool('ai_workshop_first_time_tip_shown') ?? false;
    } catch (e, s) {
      _logger.e("加载首次提示状态失败: $e");
      firstTimeTipShown.value = false;
    }
  }

  Future<void> _saveFirstTimeTipStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('ai_workshop_first_time_tip_shown', true);
      firstTimeTipShown.value = true;
    } catch (e, s) {
      _logger.e("保存首次提示状态失败: $e");
    }
  }

  Future<void> _loadCreationIntroStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      creationIntroShown.value = prefs.getBool('creation_intro_shown') ?? false;
    } catch (e, s) {
      _logger.e("加载创作介绍弹窗状态失败: $e");
      creationIntroShown.value = false;
    }
  }

  Future<void> _saveCreationIntroStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('creation_intro_shown', true);
      creationIntroShown.value = true;
    } catch (e, s) {
      _logger.e("保存创作介绍弹窗状态失败: $e");
    }
  }
  
  Future<void> _loadAnnotationCreatedStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      hasCreatedWorkThroughAnnotation.value = prefs.getBool('has_created_work_through_annotation') ?? false;
    } catch (e, s) {
      _logger.e("加载标注创建状态失败: $e");
      hasCreatedWorkThroughAnnotation.value = false;
    }
  }

  Future<void> _saveAnnotationCreatedStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_created_work_through_annotation', true);
      hasCreatedWorkThroughAnnotation.value = true;
    } catch (e, s) {
      _logger.e("保存标注创建状态失败: $e");
    }
  }
  //endregion

  //region 杂项辅助方法
  /// 检查是否应该使用数据库缓存进行搜索。
  Future<bool> _shouldUseDatabaseCache() async {
    try {
      if (!_localDatabaseService.isDatabaseReady()) return false;
      
      final consistency = await _dataSyncService.checkDataConsistency();
      final needsSync = consistency['needsSync'] as bool? ?? true;

      if (needsSync) {
        final stats = await _localDatabaseService.getDatabaseStats();
        return (stats['totalWorks'] as int? ?? 0) > 0;
      }
      return false;
    } catch (e, s) {
      _logger.e("检查数据库缓存可用性失败: $e");
      return false;
    }
  }

  /// 获取分类键，用于存储或查询。
  String _getCategoryKey(DanceCategoryType? category) {
    if (category == null) {
      return 'all';
    }
    return 'category_${category.id}';
  }
  
  /// 预加载下一页的图片。
  Future<void> checkPreloadNextPage(int index) async {
    try {
      bool updated = await _paginationService.checkAndPreloadNextPage(index);
      if (updated) {
        _syncPaginationToUI();
        // 从 WorkItemData 中提取图片URL列表
        final imageUrls = worksForUI.map((work) => work.coverUrl ?? work.localCoverPath ?? '').where((url) => url.isNotEmpty).toList();
        await _imageOptimizationService.preloadImages(imageUrls, index);
      }
    } catch (e) {
      _logger.w("图片预加载失败: $e");
    }
  }
  
  /// 导入舞蹈作品
  Future<bool> importDanceWork(dynamic context, [GlobalKey? key]) async {
    try {
      // 直接调用导入工具，不使用DanceImportConfig
      // final config = DanceImportConfig(
      //   totalWorks: _allWorks.length,
      //   showImportOptions: showImportOptions,
      //   onComplete: refreshData,
      // );
      final config = {
        'totalWorks': _allWorks.length,
        'showImportOptions': showImportOptions.value,
        'onComplete': refreshData,
      };
      return await DanceImportUtils.importDanceWork(context, config);
    } catch (e, s) {
      _logger.e("导入舞蹈作品时出错: $e");
      return false;
    }
  }
  //endregion
}
