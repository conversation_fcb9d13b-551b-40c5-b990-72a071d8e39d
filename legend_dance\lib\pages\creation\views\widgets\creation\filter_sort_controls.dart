import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keepdance/common_widgets/unified_filter_chip.dart';
// import 'package:keepdance/common_widgets/unified_filter_chip_container.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import 'package:logger/logger.dart';
import 'package:keepdance/models/dance_category.dart';

// 注意：由于汇编代码中无法获取确切的图标常量，
// 'Icons.filter_list' 和 'Icons.sort' 是基于功能推断的占位符，
// 您可能需要将其替换为项目中的实际图标。

// class id: 1049811, size: 0x8
class UnusedClass {} // 原始汇编中存在一个未使用的类定义

// class id: 4555, size: 0x10, field offset: 0xc
class FilterSortControls extends StatelessWidget {
  const FilterSortControls({super.key});

  static late final Logger _logger = Logger();

  @override
  Widget build(BuildContext context) {
    // 依赖注入获取控制器实例
    final MyWorksController controller = Get.find<MyWorksController>();

    return RepaintBoundary(
      child: Obx(
        () {
          // 创建一个包含所有过滤芯片的列表
          List<Widget> filterChips = <Widget>[
            // “全部” 过滤芯片
            UnifiedFilterChip(
              text: "全部",
              isSelected: controller.selectedCategoryId.value == null,
              onTap: () {
                _logger.i("选择分类过滤器: 全部 (categoryId: null)");
                controller.changeFilter(null);
              },
              iconData: Icons.filter_list, // 占位符图标
              marginType: ChipMarginType.right,
            ),
          ];

          // 使用 map 和 addAll 从分类列表动态创建过滤芯片
          var categoryChips = controller.danceCategoryList
              .map<Widget>((DanceCategoryType category) {
            return UnifiedFilterChip(
              text: category.name,
              isSelected: controller.selectedCategoryId.value?.id == category.id,
              onTap: () {
                _logger.i("选择分类过滤器: ${category.name} (categoryId: ${category.id})");
                controller.changeFilter(category);
              },
              iconData: Icons.sort, // 占位符图标
              marginType: ChipMarginType.right,
            );
          });
          filterChips.addAll(categoryChips);

          // 返回包含所有过滤芯片的容器
          return UnifiedFilterChipContainer(
            child: Wrap(children: filterChips),
            children: filterChips,
            layoutMode: ContainerLayoutMode.wrap,
          );
        },
      ),
    );
  }
}

// === 检测与补全说明 ===
// 1.  **静态Logger变量**: `static late final Logger _logger;` 已被正确还原并在匿名闭包中初始化和使用。
// 2.  **`build`方法结构**: `RepaintBoundary(child: Obx(() => ...))` 的UI结构已完全还原。
// 3.  **控制器获取**: `Get.find<MyWorksController>()` 的调用逻辑根据汇编代码中的字段访问和方法调用模式被还原。
// 4.  **"全部"过滤芯片**: 第一个`UnifiedFilterChip`的创建，包括其`label`、`selected`状态判断逻辑和`onSelected`回调中的日志记录与`controller.changeFilter(null)`调用，均已还原。
// 5.  **动态分类芯片**: 通过`controller.danceCategoryList.map().toList()`（或等效的`addAll`）动态生成其余`UnifiedFilterChip`列表的逻辑已还原。
//     -   `selected`状态判断 `controller.selectedCategoryId.value?.id == category.id` 也被正确还原。
//     -   `onSelected`回调中的日志记录和`controller.changeFilter(category)`调用已还原。
// 6.  **`UnifiedFilterChipContainer`**: 最终返回的容器及其`children`、`isScrollable`和`layoutMode`属性均已根据汇编代码还原。
// 7.  **图标**: 汇编代码中引用了`IconData`对象实例，但无法确定具体是哪个图标。我使用了`Icons.filter_list`和`Icons.sort`作为占位符，这与过滤和排序功能相关，符合上下文。
//
// 经检查，所有在汇编代码中实现的功能点都已包含在上述Dart代码中，没有遗漏。
